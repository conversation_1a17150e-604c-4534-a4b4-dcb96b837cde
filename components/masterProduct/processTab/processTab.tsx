import { ChevronRight, GripVertical } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useRef, useState } from 'react';

import EditIcon from '@/assets/outline/edit';
import InfoCircle from '@/assets/outline/infoCircle';
import PlusIcon from '@/assets/outline/plus';
import ProcessIcon from '@/assets/outline/process';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import LinkButton from '@/components/common/button/linkButton';
import PrimaryButton from '@/components/common/button/primaryButton';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/components/common/dialog';
import Loader from '@/components/common/loader';
import ToggleSwitch from '@/components/common/toogleSwitch';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import AddApprovers from '@/components/document/addApprovers';
import ApprovalTypeSelector, {
  ApprovalType,
} from '@/components/document/approvalTypeSelector';
import Flow from '@/components/masterProduct_deprecated/processTab/Flowchart/flow';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { ReactFlowProvider } from '@xyflow/react';

import useFetch from '../../../hooks/useFetch';
import { TProductInfo } from '../infoTab';
import AddStepModal from './addStepModal';
import FlowchartAccordion from './Flowchart/FlowchartAccordion';
import PreviewFlow from './Flowchart/preview';
import BasicDetail from './steps/basicDetail';
import Resources from './steps/resources';
import WorkInstruction from './steps/workInstruction';

interface ApproverData {
  id: string;
  name: string;
  email: string;
  user_id?: string;
  record_id?: string;
}

// Interface to match the backend step data structure
interface Step {
  id: string;
  step_name: string;
  step_type: string;
  description: string;
  sequence_no: number;
  product?: string;
  company?: string;
}

// Interface for component mappings
interface ComponentMapping {
  [stepId: string]: {
    basicDetails: string;
    workInstructions: string;
    resources: string;
  };
}

interface ProcessTabProps {
  data: TProductInfo | undefined;
}

const ProcessTab: React.FC<ProcessTabProps> = ({ data }) => {
  // State for approval section
  const [requireApproval, setRequireApproval] = useState<boolean>(false);
  const [approvalType, setApprovalType] = useState<ApprovalType>('Sequential');
  const [approversData, setApproversData] = useState<ApproverData[]>([]);
  const [approverError, setApproverError] = useState<boolean>(false);

  // State for steps (from backend)
  const [steps, setSteps] = useState<Step[]>([]);

  // State for component mappings (managed locally)
  const [componentMappings, setComponentMappings] = useState<ComponentMapping>(
    {},
  );

  const [activeStep, setActiveStep] = useState<string>(''); // Will be set when data is loaded
  const [activeComponent, setActiveComponent] = useState<string>(''); // Will be set when data is loaded

  // Component types
  const componentTypes = {
    BASIC_DETAILS: 'basic-details',
    WORK_INSTRUCTIONS: 'work-instructions',
    RESOURCES: 'resources',
  };
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);
  const [isHovering, setIsHovering] = useState<number | null>(null);
  const [isAddStepModalOpen, setIsAddStepModalOpen] = useState(false);

  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);

  const [editFlow, setEditFlow] = useState<boolean>(false);
  const router = useRouter();
  const {
    data: processData,
    isLoading: processLoading,
    reFetch,
  } = useFetch(accessToken, `products/${router.query.productId}/flowcharts`);

  interface ProcessStepResponse {
    steps: Step[];
  }

  const {
    data: processStep,
    isLoading: processStepLoading,
    reFetch: reFetchProcessSteps,
  } = useFetch<ProcessStepResponse>(
    accessToken,
    `products/${router.query.productId}/process_steps`,
  );

  // Track if this is the first render
  const isFirstRender = useRef(true);
  // Track the previous steps to detect new steps
  const prevSteps = useRef<Step[]>([]);

  // Initialize component mappings when processStep data is loaded
  useEffect(() => {
    if (processStep?.steps && processStep.steps.length > 0) {
      // Set steps from backend data
      setSteps(processStep.steps);

      // Initialize component mappings
      const newComponentMappings: ComponentMapping = {};

      processStep.steps.forEach((step: Step) => {
        newComponentMappings[step.id] = {
          basicDetails: `${step.id}-basic-details`,
          workInstructions: `${step.id}-work-instructions`,
          resources: `${step.id}-resources`,
        };
      });

      setComponentMappings(newComponentMappings);

      // Check if this is the first render with data
      if (isFirstRender.current) {
        console.log('First render - selecting first step');
        // On first render, select the first step's basic details
        const firstStepId = processStep.steps[0].id;
        setActiveStep(firstStepId);
        setActiveComponent(
          newComponentMappings[firstStepId]?.basicDetails || '',
        );
        isFirstRender.current = false;
      }
      // Check if a new step was added by comparing current steps with previous steps
      else if (prevSteps.current.length < processStep.steps.length) {
        console.log('New step detected - selecting new step');
        // Find the new step (the one that wasn't in the previous steps array)
        const newStep = processStep.steps.find(
          (step) =>
            !prevSteps.current.some((prevStep) => prevStep.id === step.id),
        );

        if (newStep) {
          // Select the new step's basic details
          setActiveStep(newStep.id);
          setActiveComponent(
            newComponentMappings[newStep.id]?.basicDetails || '',
          );
        }
      }

      // Update the previous steps reference
      prevSteps.current = [...processStep.steps];
    }
  }, [processStep]);

  // Handle drag start
  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  // Handle drag over
  const handleDragOver = (
    e: React.DragEvent<HTMLDivElement>,
    index: number,
  ) => {
    e.preventDefault();

    if (draggingIndex === null || draggingIndex === index) {
      return;
    }

    const newSteps = [...steps];
    const draggedItem = newSteps[draggingIndex];

    // Remove the dragged item
    newSteps.splice(draggingIndex, 1);
    // Insert it at the new position
    newSteps.splice(index, 0, draggedItem);

    // Update the sequence numbers
    newSteps.forEach((step, i) => {
      step.sequence_no = i + 1;
    });

    setSteps(newSteps);
    setDraggingIndex(index);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggingIndex(null);
  };

  // Handle save from the modal
  const handleSaveNewStep = () => {
    // The API call is handled in the AddStepModal component
    // After successful API call, we'll refresh the process steps data
    reFetchProcessSteps();
    setIsAddStepModalOpen(false);

    // Note: The new step selection is now handled in the useEffect that watches processStep
    // It detects when a new step is added and automatically selects it
  };
  // Check if terminal start/stop are already used
  const isTerminalStartUsedForNew = steps.some(
    (s) => s.step_type === 'terminal_start',
  );

  const isTerminalStopUsedForNew = steps.some(
    (s) => s.step_type === 'terminal_stop',
  );

  return (
    <div>
      {/* Add Step Modal */}
      <AddStepModal
        open={isAddStepModalOpen}
        onOpenChange={setIsAddStepModalOpen}
        onSave={handleSaveNewStep}
        isTerminalStartUsed={isTerminalStartUsedForNew}
        isTerminalStopUsed={isTerminalStopUsedForNew}
      />

      {/* <FlowChartModal
        editFlow={editFlow}
        setEditFlow={setEditFlow}
        handleConfirm={undefined}
        data={processData}
        reFetch={reFetch}
      /> */}
      {processLoading ? (
        <Loader className="h-[700px]" />
      ) : (
        <div className="w-300 h-90 gap-5 mb-10">
          <FlowchartAccordion
            data={processData as any}
            title="Process flowchart"
            placeholder="Create process flowchart"
          >
            {/* This is the content that will be shown when the accordion is open */}
            <ReactFlowProvider>
              <PreviewFlow data={processData} />
              {(hasAccess(
                AccessActions.CanEditSpecificProduct,
                user,
                data?.assignees?.some(
                  (assignee: any) => assignee.id === user?.id,
                ),
              ) ||
                true) && (
                <Dialog open={editFlow} onOpenChange={setEditFlow}>
                  <DialogTrigger>
                    <div className=" absolute top-2 right-2  ">
                      <PrimaryButton
                        onClick={() => setEditFlow(true)}
                        text="Edit"
                        size="medium"
                        icon={<EditIcon color="#fff" width="18" height="18" />}
                      />
                    </div>
                  </DialogTrigger>
                  <DialogContent
                    className="!max-h-[90vh] h-[90vh] !max-w-[90vw] "
                    showClose={false}
                  >
                    <ReactFlowProvider>
                      <Flow
                        setEditFlow={setEditFlow}
                        data={processData}
                        reFetch={reFetch}
                      />
                    </ReactFlowProvider>
                  </DialogContent>
                </Dialog>
              )}
            </ReactFlowProvider>
          </FlowchartAccordion>

          <div className="flex gap-4 text-lg text-medium text-dark">
            <div className=" min-w-12 w-12 h-12 rounded bg-white-150 flex items-center justify-center">
              <ProcessIcon />
            </div>
            <div>
              <div className="text-lg font-medium text-dark-300 mb-1">
                Process steps
              </div>
              <div className="text-sm text-grey-300 font-medium">
                Open the drop down below to create or view the flowchart
              </div>
            </div>
          </div>
          <div className="flex mt-5 gap-3">
            <div className="border border-white-300 rounded-lg p-3 w-2/6 flex flex-col">
              <div className="flex justify-between items-center text-sm font-medium text-grey-300 mb-2.5 pb-2.5 border-b border-white-200">
                <span>STEPS</span>
              </div>
              <div className="flex-1">
                <Accordion
                  type="single"
                  collapsible
                  className="w-full"
                  value={activeStep}
                  onValueChange={setActiveStep}
                >
                  {processStep?.steps?.map((step: Step, index: number) => (
                    <AccordionItem
                      key={step.id}
                      value={step.id}
                      className="border-none"
                      draggable={true}
                      data-step-item="true"
                      onDragStart={() => handleDragStart(index)}
                      onDragOver={(e) => handleDragOver(e, index)}
                      onDragEnd={handleDragEnd}
                    >
                      <AccordionTrigger
                        className="hover:bg-white-150 hover:shadow-none !border-none"
                        triggerClassname="!data-[state=open]:border-none"
                      >
                        <div
                          className="w-full flex items-center gap-2"
                          onMouseEnter={() => setIsHovering(index)}
                          onMouseLeave={() => setIsHovering(null)}
                        >
                          <div className="h-8 w-8 flex items-center justify-center text-sm font-medium text-dark-300 bg-white-150 cursor-grab">
                            {isHovering === index ? (
                              <GripVertical className="h-5 w-5 text-gray-400" />
                            ) : (
                              String(step.sequence_no).padStart(2, '0')
                            )}
                          </div>
                          <div>{step.step_name}</div>
                        </div>
                      </AccordionTrigger>

                      <AccordionContent className="p-2 mt-1">
                        <div className="relative">
                          <div className="h-full w-0 border-r border-[#D0D5DD] absolute top-0 left-0"></div>
                          <div className="pl-3 flex flex-col gap-2">
                            <div
                              className={`text-base font-medium leading-6 text-dark-300 px-2.5 py-2 rounded-lg hover:bg-white-150 cursor-pointer flex items-center justify-between flex-1 ${
                                activeComponent ===
                                componentMappings[step.id]?.basicDetails
                                  ? 'bg-primary-100 hover:bg-primary-100'
                                  : ''
                              }`}
                              onClick={() =>
                                setActiveComponent(
                                  componentMappings[step.id]?.basicDetails ||
                                    '',
                                )
                              }
                            >
                              Basic Details
                              {activeComponent ===
                              componentMappings[step.id]?.basicDetails ? (
                                <ChevronRight size={20} />
                              ) : (
                                ''
                              )}
                            </div>
                            <div
                              className={`text-base font-medium leading-6 text-dark-300 px-2.5 py-2 rounded-lg hover:bg-white-150 cursor-pointer flex items-center justify-between flex-1 ${
                                activeComponent ===
                                componentMappings[step.id]?.workInstructions
                                  ? 'bg-primary-100 hover:bg-primary-100'
                                  : ''
                              }`}
                              onClick={() =>
                                setActiveComponent(
                                  componentMappings[step.id]
                                    ?.workInstructions || '',
                                )
                              }
                            >
                              Work Instructions
                              {activeComponent ===
                              componentMappings[step.id]?.workInstructions ? (
                                <ChevronRight size={20} />
                              ) : (
                                ''
                              )}
                            </div>
                            <div
                              className={`text-base font-medium leading-6 text-dark-300 px-2.5 py-2 rounded-lg hover:bg-white-150 cursor-pointer flex items-center justify-between flex-1 ${
                                activeComponent ===
                                componentMappings[step.id]?.resources
                                  ? 'bg-primary-100 hover:bg-primary-100'
                                  : ''
                              }`}
                              onClick={() =>
                                setActiveComponent(
                                  componentMappings[step.id]?.resources || '',
                                )
                              }
                            >
                              Resources
                              {activeComponent ===
                              componentMappings[step.id]?.resources ? (
                                <ChevronRight size={20} />
                              ) : (
                                ''
                              )}
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
              <div className="p-5">
                <LinkButton
                  text={'Add new step'}
                  icon={<PlusIcon />}
                  buttonClasses="w-full"
                  onClick={() => setIsAddStepModalOpen(true)}
                />
              </div>
            </div>
            <div className="w-4/6">
              {steps.map((step) => (
                <React.Fragment key={`components-${step.id}`}>
                  {/* Basic Details */}
                  {activeComponent ===
                    componentMappings[step.id]?.basicDetails && (
                    <BasicDetail
                      stepId={`${step.id}-basic-details`}
                      step={step}
                      allSteps={steps}
                      onSave={(data) => {
                        console.log(
                          `Step ${step.sequence_no} Basic Details saved:`,
                          data,
                        );

                        // Store the current active component
                        const currentActiveComponent = activeComponent;

                        // Refresh the steps data after saving
                        reFetchProcessSteps();

                        // Restore the active component after a short delay to ensure the refresh is complete
                        setTimeout(() => {
                          setActiveComponent(currentActiveComponent);
                        }, 100);
                      }}
                    />
                  )}

                  {/* Work Instructions */}
                  {activeComponent ===
                    componentMappings[step.id]?.workInstructions && (
                    <WorkInstruction
                      stepId={`${step.id}-work-instructions`}
                      step={step}
                      allSteps={steps}
                      onSave={(data) => {
                        console.log(
                          `Step ${step.sequence_no} Work Instructions saved:`,
                          data,
                        );

                        // Store the current active component
                        const currentActiveComponent = activeComponent;

                        // Refresh the steps data after saving
                        reFetchProcessSteps();

                        // Restore the active component after a short delay to ensure the refresh is complete
                        setTimeout(() => {
                          setActiveComponent(currentActiveComponent);
                        }, 100);
                      }}
                    />
                  )}

                  {/* Resources */}
                  {activeComponent ===
                    componentMappings[step.id]?.resources && (
                    <Resources
                      stepId={`${step.id}-resources`}
                      step={step}
                      onSave={(data) => {
                        console.log(
                          `Step ${step.sequence_no} Resources saved:`,
                          data,
                        );

                        // Store the current active component
                        const currentActiveComponent = activeComponent;

                        // Refresh the steps data after saving
                        reFetchProcessSteps();

                        // Restore the active component after a short delay to ensure the refresh is complete
                        setTimeout(() => {
                          setActiveComponent(currentActiveComponent);
                        }, 100);
                      }}
                    />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>

          <div className="flex gap-4 text-lg text-medium text-dark my-6 mt-9">
            <div className=" min-w-12 w-12 h-12 rounded bg-white-150 flex items-center justify-center">
              <ProcessIcon />
            </div>
            <div>
              <div className="text-lg font-medium text-dark-300 mb-1">
                Approval Flow
              </div>
              <div className="text-sm text-grey-300 font-medium">
                Open the drop down below to create or view the flowchart
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-16 justify-between mb-5 bg-gray-50 px-6 py-5 rounded-xl">
              <div>
                <div className="flex items-center gap-2 flex-nowrap">
                  <h3 className="text-base font-medium text-approval-text-primary">
                    Requires approval
                  </h3>

                  <Tooltip>
                    <TooltipTrigger>
                      <div
                        className="w-10 h-10 flex items-center justify-center rounded-full cursor-pointer"
                        onClick={() => console.log('first')}
                      >
                        <InfoCircle />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-sm text-dark-300">Test</div>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <p className="text-grey-300">Enable if approval is required</p>
              </div>
              <div>
                <ToggleSwitch
                  initialState={requireApproval}
                  onChange={(state) => {
                    setRequireApproval(state);
                  }}
                />
              </div>
            </div>
          </div>
          {requireApproval && (
            <>
              <div className="mb-5">
                <div className="w-full">
                  <ApprovalTypeSelector
                    value={approvalType}
                    onChange={setApprovalType}
                  />
                </div>
              </div>

              <AddApprovers
                approvalType={approvalType}
                approversData={(approvers: ApproverData[]) =>
                  setApproversData(approvers)
                }
              />
              {approverError ? (
                <div className="text-xs font-semibold leading-5 text-left text-red-200">
                  At least one approver is required
                </div>
              ) : (
                <></>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default ProcessTab;
