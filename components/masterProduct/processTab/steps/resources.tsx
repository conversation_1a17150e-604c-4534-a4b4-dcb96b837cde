import { ArrowRight } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import SecondaryButton from '@/components/common/button/secondaryButton';
import Tabs from '@/components/common/tabs';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePut } from '@/hooks/usePut';

import AssetsTab from './resources/assetsTab';
import LinkDocumentsTab from './resources/linkDocumentsTab';
import MaterialTab from './resources/materialTab';
import PeopleTab from './resources/peopleTab';

interface ResourcesProps {
  stepId?: string;
  step?: {
    id: string;
  };
  onSave?: (data: ResourcesState) => void;
}

// Define the resource data structure
interface Material {
  material_id: string;
  quantity: number;
}

interface ApiMaterial {
  id: string;
  material_id: string;
  material_name: string;
  quantity: number;
}

interface ApiPerson {
  id: string;
  employee_id: string;
  employee_name: string;
}

interface ApiAsset {
  id: string;
  asset_id: string;
  asset_name: string;
}

interface ApiDocument {
  id: string;
  document_id: string;
  document_name: string;
}

interface ApiAttachment {
  id: string;
  file_path: string;
  file_extension: string;
}

interface ResourcesData {
  materials: ApiMaterial[];
  people_responsible: ApiPerson[];
  approvers: ApiPerson[];
  assets: ApiAsset[];
  documents: ApiDocument[];
  attachments: ApiAttachment[];
}

interface Attachment {
  id?: string; // Optional since it's no longer supported
  file_path: string;
  file_extension: string;
  name?: string;
  type: 'independent';
}

interface ResourcesState {
  activeTab: number;
  materials: Material[];
  people_responsible: string[];
  approvers: string[];
  assets: string[];
  documents: string[];
  attachments: Attachment[];
}

// Create a store to persist data across component unmounts
const resourcesStore: Record<string, ResourcesState> = {};

const Resources: React.FC<ResourcesProps> = ({
  stepId = 'default',
  step,
  onSave,
}) => {
  const router = useRouter();
  const { productId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);

  // Initialize the PUT hook
  const {
    putData,
    isLoading,
    response: putResponse,
    error: putError,
  } = usePut();

  // State for resources
  const [activeTab, setActiveTab] = useState(() => {
    return resourcesStore[stepId]?.activeTab || 0;
  });
  const [materials, setMaterials] = useState<Material[]>([]);
  const [peopleResponsible, setPeopleResponsible] = useState<string[]>([]);
  const [approvers, setApprovers] = useState<string[]>([]);
  const [assets, setAssets] = useState<string[]>([]);
  const [documents, setDocuments] = useState<string[]>([]);
  const [attachments, setAttachments] = useState<Attachment[]>([]);

  // Fetch resources data
  const { data: resourcesData, reFetch: reFetchResources } =
    useFetch<ResourcesData>(
      accessToken,
      step && productId
        ? `products/${productId}/process_step/${step.id}/resources`
        : undefined,
    );

  // Log when resources data is loaded
  useEffect(() => {
    if (resourcesData) {
      console.log('Resources data loaded:', resourcesData);
    }
  }, [resourcesData]);

  // Initialize from store if available
  useEffect(() => {
    if (resourcesStore[stepId]) {
      // Always restore the active tab from the store
      setActiveTab(resourcesStore[stepId].activeTab || 0);

      // For materials, people, and assets, use store data if available, otherwise use API data
      if (resourcesStore[stepId].materials?.length > 0) {
        setMaterials(resourcesStore[stepId].materials);
      } else if (resourcesData?.materials) {
        const mappedMaterials = resourcesData.materials.map((material) => ({
          material_id: material.material_id,
          quantity: material.quantity,
        }));
        setMaterials(mappedMaterials);
      }

      if (resourcesStore[stepId].people_responsible?.length > 0) {
        setPeopleResponsible(resourcesStore[stepId].people_responsible);
      } else if (resourcesData?.people_responsible) {
        const peopleIds = resourcesData.people_responsible.map(
          (person) => person.employee_id,
        );
        setPeopleResponsible(peopleIds);
      }

      if (resourcesStore[stepId].approvers?.length > 0) {
        setApprovers(resourcesStore[stepId].approvers);
      } else if (resourcesData?.approvers) {
        const approverIds = resourcesData.approvers.map(
          (person) => person.employee_id,
        );
        setApprovers(approverIds);
      }

      if (resourcesStore[stepId].assets?.length > 0) {
        setAssets(resourcesStore[stepId].assets);
      } else if (resourcesData?.assets) {
        const assetIds = resourcesData.assets.map((asset) => asset.asset_id);
        setAssets(assetIds);
      }

      if (resourcesStore[stepId].documents?.length > 0) {
        setDocuments(resourcesStore[stepId].documents);
      } else if (resourcesData?.documents) {
        const documentIds = resourcesData.documents.map(
          (doc) => doc.document_id,
        );
        setDocuments(documentIds);
      }

      if (resourcesStore[stepId].attachments?.length > 0) {
        setAttachments(resourcesStore[stepId].attachments);
      } else if (resourcesData?.attachments) {
        const mappedAttachments = resourcesData.attachments.map(
          (attachment) => ({
            id: attachment.id,
            file_path: attachment.file_path,
            file_extension: attachment.file_extension,
            name: `File ${attachment.id.substring(0, 8)}`,
            type: 'independent' as const,
          }),
        );
        setAttachments(mappedAttachments);
      }
    } else if (resourcesData) {
      // If no store data but API data is available, use API data
      if (resourcesData.materials) {
        const mappedMaterials = resourcesData.materials.map((material) => ({
          material_id: material.material_id,
          quantity: material.quantity,
        }));
        setMaterials(mappedMaterials);
      }

      if (resourcesData.people_responsible) {
        const peopleIds = resourcesData.people_responsible.map(
          (person) => person.employee_id,
        );
        setPeopleResponsible(peopleIds);
      }

      if (resourcesData.approvers) {
        const approverIds = resourcesData.approvers.map(
          (person) => person.employee_id,
        );
        setApprovers(approverIds);
      }

      if (resourcesData.assets) {
        const assetIds = resourcesData.assets.map((asset) => asset.asset_id);
        setAssets(assetIds);
      }

      if (resourcesData.documents) {
        const documentIds = resourcesData.documents.map(
          (doc) => doc.document_id,
        );
        setDocuments(documentIds);
      }

      if (resourcesData.attachments) {
        const mappedAttachments = resourcesData.attachments.map(
          (attachment) => ({
            id: attachment.id,
            file_path: attachment.file_path,
            file_extension: attachment.file_extension,
            name: `File ${attachment.id.substring(0, 8)}`,
            type: 'independent' as const,
          }),
        );
        setAttachments(mappedAttachments);
      }
    }
  }, [stepId, resourcesData]);

  // Save to store whenever state changes
  useEffect(() => {
    resourcesStore[stepId] = {
      activeTab,
      materials,
      people_responsible: peopleResponsible,
      approvers,
      assets,
      documents,
      attachments,
    };
  }, [
    activeTab,
    materials,
    peopleResponsible,
    approvers,
    assets,
    documents,
    attachments,
    stepId,
  ]);

  // Handle API response
  useEffect(() => {
    if (putResponse) {
      toast.success('Resources saved successfully');

      // Refresh resources data
      reFetchResources();

      // Call onSave callback if provided
      if (onSave) {
        onSave({
          activeTab,
          materials,
          people_responsible: peopleResponsible,
          approvers,
          assets,
          documents,
          attachments,
        });
      }
    }
  }, [putResponse]);

  // Handle API error
  useEffect(() => {
    if (putError) {
      toast.error('Failed to save resources');
      console.error('Error saving resources:', putError);
    }
  }, [putError]);

  const handleSaveAndNext = () => {
    console.log('Save and next clicked');

    // Save current state to store
    resourcesStore[stepId] = {
      activeTab,
      materials,
      people_responsible: peopleResponsible,
      approvers,
      assets,
      documents,
      attachments,
    };

    // If we have a step, update it via API
    if (step && productId) {
      // Ensure we're sending the correct payload format
      const apiBody = {
        materials: materials.map((m) => ({
          material_id: m.material_id,
          quantity: m.quantity,
        })),
        people_responsible: peopleResponsible.filter(
          (id) =>
            // Only include valid UUIDs or email addresses
            id.indexOf('@') > -1 ||
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
              id,
            ),
        ),
        approvers: approvers.filter(
          (id) =>
            // Only include valid UUIDs or email addresses
            id.indexOf('@') > -1 ||
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
              id,
            ),
        ),
        assets: assets.filter((id) =>
          // Only include valid UUIDs
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
            id,
          ),
        ),
        documents: documents.filter((id) =>
          // Only include valid UUIDs
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
            id,
          ),
        ),
        attachments: attachments.map((attachment) => {
          // For all attachments, only send file_path and file_extension
          return {
            file_path: attachment.file_path,
            file_extension: attachment.file_extension,
          };
        }),
      };

      console.log('Sending API payload:', apiBody);

      // Make the API call
      putData(
        accessToken as string,
        `products/${productId}/process_step/${step.id}`,
        apiBody,
      );
    } else {
      // If no step data (shouldn't happen), just call onSave
      if (onSave) {
        onSave({
          activeTab,
          materials,
          people_responsible: peopleResponsible,
          approvers,
          assets,
          documents,
          attachments,
        });
      }
    }
  };

  const tabsData = [
    { name: 'Material', textColor: 'text-dark-100' },
    { name: 'People', textColor: 'text-dark-100' },
    { name: 'Assets', textColor: 'text-dark-100' },
    { name: 'Link documents', textColor: 'text-dark-100' },
  ];

  return (
    <div className="pt-6 pb-0 rounded-lg border border-white-300 h-[35.5rem] flex flex-col justify-between">
      <div className="px-6 flex-grow overflow-y-auto">
        <div className="border-b border-white-300 mb-6">
          <Tabs
            tabsData={tabsData}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            tabGroupName="resources"
          />
        </div>

        {activeTab === 0 && (
          <MaterialTab
            materials={materials.map((m) => {
              // Find the material name from the API response if available
              const apiMaterial = resourcesData?.materials?.find(
                (apiM) => apiM.material_id === m.material_id,
              );

              return {
                id: m.material_id || '',
                name:
                  apiMaterial?.material_name ||
                  `Material ${m.material_id?.substring(0, 8) || ''}`,
                quantity: m.quantity.toString(),
                unit: 'Kgs',
                material_id: m.material_id,
              };
            })}
            onMaterialsChange={(updatedMaterials) => {
              // Convert UI materials to API format
              const apiMaterials = updatedMaterials.map((m) => ({
                material_id: m.material_id || m.id,
                quantity: parseInt(m.quantity) || 0,
              }));
              setMaterials(apiMaterials);
            }}
          />
        )}
        {activeTab === 1 && (
          <>
            <div className="mb-6">
              <PeopleTab
                title="Add People Responsible"
                placeholder="Search employees"
                people={peopleResponsible.map((employeeId) => {
                  // Find the employee name from the API response if available
                  const apiPerson = resourcesData?.people_responsible?.find(
                    (person) => person.employee_id === employeeId,
                  );

                  return {
                    id: employeeId,
                    role: apiPerson?.employee_name || employeeId,
                  };
                })}
                onPeopleChange={(updatedPeople) => {
                  // Filter out any non-UUID values (like timestamp-based IDs)
                  const validPeople = updatedPeople.filter(
                    (id) =>
                      // Check if it's a valid UUID format or an email
                      id.includes('@') ||
                      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
                        id,
                      ),
                  );
                  setPeopleResponsible(validPeople);
                }}
              />
            </div>

            <div>
              <PeopleTab
                title="Add Approvers"
                placeholder="Search employees"
                people={approvers.map((employeeId) => {
                  // Find the employee name from the API response if available
                  const apiPerson = resourcesData?.approvers?.find(
                    (person) => person.employee_id === employeeId,
                  );

                  return {
                    id: employeeId,
                    role: apiPerson?.employee_name || employeeId,
                  };
                })}
                onPeopleChange={(updatedPeople) => {
                  // Filter out any non-UUID values (like timestamp-based IDs)
                  const validPeople = updatedPeople.filter(
                    (id) =>
                      // Check if it's a valid UUID format or an email
                      id.includes('@') ||
                      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
                        id,
                      ),
                  );
                  setApprovers(validPeople);
                }}
              />
            </div>
          </>
        )}
        {activeTab === 2 && (
          <AssetsTab
            assets={assets.map((assetId) => {
              // Find the asset name from the API response if available
              const apiAsset = resourcesData?.assets?.find(
                (asset) => asset.asset_id === assetId,
              );

              return {
                id: assetId,
                asset_name:
                  apiAsset?.asset_name || `Asset ${assetId.substring(0, 8)}`,
              };
            })}
            onAssetsChange={(updatedAssets) => {
              // Filter out any non-UUID values (like timestamp-based IDs)
              const validAssets = updatedAssets.filter((id) =>
                /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
                  id,
                ),
              );
              setAssets(validAssets);
            }}
          />
        )}
        {activeTab === 3 && (
          <LinkDocumentsTab
            documents={documents.map((documentId) => {
              // Find the document name from the API response if available
              const apiDocument = resourcesData?.documents?.find(
                (doc) => doc.document_id === documentId,
              );

              return {
                id: documentId,
                name:
                  apiDocument?.document_name ||
                  `Document ${documentId.substring(0, 8)}`,
                type: 'doc_hub',
              };
            })}
            attachments={attachments.map((attachment) => ({
              id: attachment.id,
              name: attachment.name,
              type: attachment.type,
              file_path: attachment.file_path,
              file_extension: attachment.file_extension,
            }))}
            onDocumentsChange={(updatedDocuments) => {
              // Filter out any non-UUID values
              const validDocuments = updatedDocuments.filter((id) =>
                /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
                  id,
                ),
              );
              setDocuments(validDocuments);
            }}
            onAttachmentsChange={(updatedAttachments) => {
              console.log('Received updated attachments:', updatedAttachments);
              setAttachments(updatedAttachments);
            }}
          />
        )}
      </div>

      <div className="flex justify-end py-4 px-6 shadow-[0px_0px_1px_0px_#3031330D,_0px_-4px_16px_0px_#30313314]">
        <SecondaryButton
          size="medium"
          text={isLoading ? 'Saving...' : 'Save'}
          icon={!isLoading && <ArrowRight size={20} />}
          iconPosition="right"
          onClick={handleSaveAndNext}
          disabled={isLoading}
        />
      </div>
    </div>
  );
};

export default Resources;
