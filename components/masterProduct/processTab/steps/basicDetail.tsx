import { ArrowRight, Info } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import SecondaryButton from '@/components/common/button/secondaryButton';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePut } from '@/hooks/usePut';

// Step interface to match backend data
interface Step {
  id: string;
  step_name: string;
  step_type: string;
  description: string;
  sequence_no: number;
  product?: string;
  company?: string;
}

interface BasicDetailProps {
  stepId?: string;
  step?: Step;
  onSave?: (data: BasicDetailFormData) => void;
  allSteps?: Step[]; // Used to check for terminal start/stop
}

export interface BasicDetailFormData {
  stepName: string;
  stepType: string;
  description: string;
}

// Create a store to persist form data across component unmounts
const formDataStore: Record<string, BasicDetailFormData> = {};

const BasicDetail: React.FC<BasicDetailProps> = ({
  stepId = 'default',
  step,
  onSave,
  allSteps = [],
}) => {
  const router = useRouter();
  const { productId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);

  // Initialize the PUT hook
  const { putData, isLoading, response, error } = usePut();

  // Map API step type to UI step type
  const mapAPITypeToUI = (apiType: string): string => {
    const typeMap: Record<string, string> = {
      terminal_start: 'Terminal start',
      step: 'Step',
      decision: 'Decision',
      terminal_stop: 'Terminal stop',
    };
    return typeMap[apiType] || '';
  };

  // Map UI step type to API step type
  const mapUITypeToAPI = (uiType: string): string => {
    const typeMap: Record<string, string> = {
      'Terminal start': 'terminal_start',
      Step: 'step',
      Decision: 'decision',
      'Terminal stop': 'terminal_stop',
    };
    return typeMap[uiType] || '';
  };

  // Initialize form data from step or store
  const [formData, setFormData] = useState<BasicDetailFormData>(() => {
    if (step) {
      // If step data is provided, use it to initialize the form
      return {
        stepName: step.step_name,
        stepType: mapAPITypeToUI(step.step_type),
        description: step.description,
      };
    }

    // Otherwise, use store or default values
    return (
      formDataStore[stepId] || {
        stepName: '',
        stepType: '',
        description: '',
      }
    );
  });

  // Check if terminal start/stop are already used
  const isTerminalStartUsed = allSteps.some(
    (s) => s.step_type === 'terminal_start' && (!step || s.id !== step.id),
  );

  const isTerminalStopUsed = allSteps.some(
    (s) => s.step_type === 'terminal_stop' && (!step || s.id !== step.id),
  );

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (value: string) => {
    setFormData((prev) => ({ ...prev, stepType: value }));
  };

  // Save form data to store whenever it changes
  useEffect(() => {
    formDataStore[stepId] = formData;
  }, [formData, stepId]);

  // Handle API response
  useEffect(() => {
    if (response) {
      console.log('Step updated successfully:', response);
      // Call onSave callback if provided
      if (onSave) {
        onSave(formData);
      }
    }
  }, [response, formData]);

  // Handle API error
  useEffect(() => {
    if (error) {
      console.error('Error updating step:', error);
    }
  }, [error]);

  const handleSaveAndNext = () => {
    console.log('Form data:', formData);
    // Save to store
    formDataStore[stepId] = formData;

    // If we have a step, update it via API
    if (step && productId) {
      const apiBody = {
        step_name: formData.stepName,
        step_type: mapUITypeToAPI(formData.stepType),
        description: formData.description,
      };

      // Make the API call
      putData(
        accessToken as string,
        `products/${productId}/process_step/${step.id}`,
        apiBody,
      );
    } else {
      // If no step data (shouldn't happen), just call onSave
      if (onSave) {
        onSave(formData);
      }
    }
  };

  return (
    <div className="pt-6 pb-0 rounded-lg border border-white-300 h-[35.5rem] flex flex-col justify-between">
      <div className="space-y-6 px-6">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="stepName"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Step name
            </Label>
            <Input
              placeholder="Enter step name"
              id="stepName"
              type="text"
              name="stepName"
              value={formData.stepName}
              onChange={handleChange}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="stepType"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Step type
            </Label>
            <Select
              onValueChange={handleSelectChange}
              value={formData.stepType}
            >
              <SelectTrigger className="w-full" id="stepType">
                <SelectValue placeholder="Step" />
              </SelectTrigger>
              <SelectContent>
                {!isTerminalStartUsed && (
                  <SelectItem value="Terminal start">Terminal start</SelectItem>
                )}
                <SelectItem value="Step">Step</SelectItem>
                <SelectItem value="Decision">Decision</SelectItem>
                {!isTerminalStopUsed && (
                  <SelectItem value="Terminal stop">Terminal stop</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="mb-4">
          <Label
            htmlFor="description"
            className="text-base font-medium leading-6 text-dark-100 mb-2.5"
          >
            Description
          </Label>
          <Textarea
            id="description"
            name="description"
            placeholder="Enter description"
            value={formData.description}
            onChange={handleChange}
            className="min-h-[100px]"
          />
        </div>

        <div className="flex items-start gap-2 py-2 px-3 bg-white-150 rounded-lg">
          <div className="bg-[#91909A29] h-9 w-9 rounded-full flex justify-center items-center">
            <Info className="w-5 h-5 text-gray-500 mt-0.5" />
          </div>
          <p className="text-sm text-dark-300 font-medium">
            Add conditional branching to the relevant work instruction by
            setting it as a validation rule.
          </p>
        </div>
      </div>

      <div className="flex justify-end py-4 px-6 shadow-[0px_0px_1px_0px_#3031330D,_0px_-4px_16px_0px_#30313314]">
        <SecondaryButton
          size="medium"
          text={isLoading ? 'Saving...' : 'Save'}
          icon={!isLoading && <ArrowRight size={20} />}
          iconPosition="right"
          onClick={handleSaveAndNext}
          disabled={isLoading}
        />
      </div>
    </div>
  );
};

export default BasicDetail;
