import { Plus, Trash2 } from 'lucide-react';
import {
  KeyboardEvent,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import CreatableSingleSelect, {
  IOption,
} from '@/components/common/creatableSelect';
import { Label } from '@/components/common/label';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import { ValueFormatterParams } from '@ag-grid-community/core';

export interface Asset {
  id: string;
  name: string;
}

interface AssetsTabProps {
  assets: Asset[] | string[];
  onAssetsChange: (assets: string[]) => void;
}

interface AssetSectionProps {
  title: string;
  placeholder: string;
  assets: Asset[];
  onAddAsset: (name: string, selectedOption?: IOption) => void;
  onRemoveAsset: (id: string) => void;
}

const AssetSection = ({
  title,
  placeholder,
  assets,
  onAddAsset,
  onRemoveAsset,
}: AssetSectionProps) => {
  const [selectedAsset, setSelectedAsset] = useState<IOption | undefined>(
    undefined,
  );

  const handleAssetChange = useCallback((option: IOption) => {
    setSelectedAsset(option);
  }, []);

  const handleAddAsset = useCallback(() => {
    if (selectedAsset?.label) {
      onAddAsset(selectedAsset.label, selectedAsset);
      setSelectedAsset(undefined);
    }
  }, [selectedAsset, onAddAsset]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === 'Enter' && selectedAsset?.label) {
        e.preventDefault();
        onAddAsset(selectedAsset.label, selectedAsset);
        setSelectedAsset(undefined);
      }
    },
    [selectedAsset, onAddAsset],
  );

  const columnDefs = useMemo(() => {
    return [
      {
        headerName: 'Asset',
        field: 'name',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: Record<string, unknown>) =>
          params.value as string,
        valueFormatter: (params: ValueFormatterParams) =>
          params.value as string,
        filter: true,
        flex: 2,
      },
      {
        headerName: 'Manage',
        field: 'manage',
        sortable: false,
        resizable: false,
        getQuickFilterText: () => '',
        valueFormatter: () => '',
        filter: false,
        cellRenderer: (params: Record<string, unknown>) => (
          <div className="flex justify-center items-center h-full">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onRemoveAsset((params.data as Asset).id);
              }}
              className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
              title="Delete"
            >
              <Trash2 height={16} />
            </button>
          </div>
        ),
        width: 120,
      },
    ];
  }, [onRemoveAsset]);

  return (
    <div className=" rounded-lg">
      <Label className="text-base font-medium text-dark-100 mb-2 block">
        {title}
      </Label>

      <div className="flex items-center gap-4 mb-2">
        <div className="flex-1">
          <div onKeyDown={handleKeyDown}>
            <CreatableSingleSelect
              placeholder={placeholder}
              selectedOption={selectedAsset}
              onChange={handleAssetChange}
              endpoint="assets"
              isCreatable={false} // Disable creation of new options
            />
          </div>
        </div>
        <SecondaryButton
          size="medium"
          text="Add Asset"
          icon={<Plus size={20} />}
          iconPosition="left"
          onClick={handleAddAsset}
        />
      </div>

      {assets.length === 0 ? (
        <div className="text-base text-grey-200 font-medium mt-5">
          Search to add assets
        </div>
      ) : (
        <div className="mt-5">
          <CommonTable
            data={{ records: assets }}
            columnDefs={columnDefs}
            searchBox={false}
            paginate={false}
          />
        </div>
      )}
    </div>
  );
};

// Assets tab content
const AssetsTab: React.FC<AssetsTabProps> = ({ assets, onAssetsChange }) => {
  // Convert assets array to Asset objects for UI
  const [primaryAssets, setPrimaryAssets] = useState<Asset[]>(() => {
    // Check if assets is already an array of Asset objects
    if (assets.length > 0 && typeof assets[0] === 'object') {
      return assets as Asset[];
    }

    // Otherwise, convert string array to Asset objects
    return (assets as string[]).map((assetId) => ({
      id: assetId,
      name: `Asset ${assetId.substring(0, 8)}`,
    }));
  });

  // We're only using primary assets for now
  const [secondaryAssets, setSecondaryAssets] = useState<Asset[]>([]);

  // Update parent component when assets change
  useEffect(() => {
    const assetIds = primaryAssets.map((asset) => asset.id);
    onAssetsChange(assetIds);
  }, [primaryAssets]);

  // Function to generate a UUID v4
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0,
          v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      },
    );
  };

  const handleAddPrimaryAsset = useCallback(
    (name: string, selectedOption?: IOption) => {
      // Check if asset already exists
      const assetExists = primaryAssets.some((asset) => asset.name === name);

      if (!assetExists) {
        const newAsset = {
          // Use the value from CreatableSingleSelect if available (which should be a UUID)
          id: selectedOption?.value || generateUUID(),
          name,
        };
        setPrimaryAssets((prev) => [...prev, newAsset]);
      }
    },
    [primaryAssets],
  );

  const handleRemovePrimaryAsset = useCallback((assetId: string) => {
    setPrimaryAssets((prev) => prev.filter((asset) => asset.id !== assetId));
  }, []);

  const handleAddSecondaryAsset = useCallback(
    (name: string, selectedOption?: IOption) => {
      // Check if asset already exists
      const assetExists = secondaryAssets.some((asset) => asset.name === name);

      if (!assetExists) {
        const newAsset = {
          // Use the value from CreatableSingleSelect if available (which should be a UUID)
          id: selectedOption?.value || generateUUID(),
          name,
        };
        setSecondaryAssets((prev) => [...prev, newAsset]);
      }
    },
    [secondaryAssets],
  );

  const handleRemoveSecondaryAsset = useCallback((assetId: string) => {
    setSecondaryAssets((prev) => prev.filter((asset) => asset.id !== assetId));
  }, []);

  return (
    <div className="space-y-6">
      <AssetSection
        title="Add Primary Assets"
        placeholder="Search assets"
        assets={primaryAssets}
        onAddAsset={handleAddPrimaryAsset}
        onRemoveAsset={handleRemovePrimaryAsset}
      />
    </div>
  );
};

export default AssetsTab;
