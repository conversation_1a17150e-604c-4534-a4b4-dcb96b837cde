import { Plus, X } from 'lucide-react';
import { KeyboardEvent, useCallback, useEffect, useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import CreatableSingleSelect, {
  IOption,
} from '@/components/common/creatableSelect';
import { Label } from '@/components/common/label';

export interface Person {
  id: string;
  role: string;
}

interface PeopleSectionProps {
  title: string;
  placeholder: string;
  employees: Person[];
  onAddEmployee: (role: string, selectedOption?: IOption) => void;
  onRemoveEmployee: (id: string) => void;
}

interface PeopleTabProps {
  people: Person[] | string[];
  onPeopleChange: (people: string[]) => void;
  title?: string;
  placeholder?: string;
}

const PeopleSection = ({
  title,
  placeholder,
  employees,
  onAddEmployee,
  onRemoveEmployee,
}: PeopleSectionProps) => {
  const [selectedEmployee, setSelectedEmployee] = useState<IOption | undefined>(
    undefined,
  );

  const handleEmployeeChange = useCallback((option: IOption) => {
    setSelectedEmployee(option);
  }, []);

  const handleAddEmployee = useCallback(() => {
    if (selectedEmployee?.label) {
      onAddEmployee(selectedEmployee.label, selectedEmployee);
      setSelectedEmployee(undefined);
    }
  }, [selectedEmployee, onAddEmployee]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === 'Enter' && selectedEmployee?.label) {
        e.preventDefault();
        onAddEmployee(selectedEmployee.label, selectedEmployee);
        setSelectedEmployee(undefined);
      }
    },
    [selectedEmployee, onAddEmployee],
  );

  return (
    <div className="border border-white-300 rounded-lg p-5 mb-5">
      <Label className="text-base font-medium text-dark-100 mb-2 block">
        {title}
      </Label>

      <div className="flex items-center gap-4 mb-2">
        <div className="flex-1">
          <div onKeyDown={handleKeyDown}>
            <CreatableSingleSelect
              placeholder={placeholder}
              selectedOption={selectedEmployee}
              onChange={handleEmployeeChange}
              endpoint="employees"
              isCreatable={false} // Disable creation of new options
            />
          </div>
        </div>
        <PrimaryButton
          size="medium"
          text="Add employee"
          icon={<Plus size={20} />}
          iconPosition="left"
          onClick={handleAddEmployee}
        />
      </div>

      {employees.length === 0 && (
        <div className="text-base text-grey-200 font-medium mt-5">
          Search to add people
        </div>
      )}

      {employees.length > 0 && (
        <div className="flex flex-wrap gap-2.5 mt-5">
          {employees.map((employee) => (
            <div
              key={employee.id}
              className="flex items-center bg-white-200 rounded-md px-2 py-1 gap-2"
            >
              <span className="text-base text-dark-300">{employee.role}</span>
              <button onClick={() => onRemoveEmployee(employee.id)}>
                <X size={16} />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// People tab content
const PeopleTab: React.FC<PeopleTabProps> = ({
  people,
  onPeopleChange,
  title = 'Add People',
  placeholder = 'Search employees',
}) => {
  // Convert people array to Person objects for UI
  const [employees, setEmployees] = useState<Person[]>(() => {
    // Check if people is already an array of Person objects
    if (people.length > 0 && typeof people[0] === 'object') {
      return people as Person[];
    }

    // Otherwise, convert string array to Person objects
    return (people as string[]).map((personId) => ({
      id: personId,
      role: personId,
    }));
  });

  // Update parent component when employees change
  useEffect(() => {
    const peopleIds = employees.map((emp) => emp.id);
    onPeopleChange(peopleIds);
  }, [employees]);

  // Function to generate a UUID v4
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0,
          v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      },
    );
  };

  const handleAddEmployee = useCallback(
    (role: string, selectedOption?: IOption) => {
      // Check if employee already exists
      const employeeExists = employees.some((emp) => emp.role === role);

      if (!employeeExists) {
        const newEmployee = {
          // Use the value from CreatableSingleSelect if available (which should be a UUID)
          // Otherwise use the role as ID (for email addresses)
          id:
            selectedOption?.value ||
            (role.includes('@') ? role : generateUUID()),
          role,
        };
        setEmployees((prev) => [...prev, newEmployee]);
      }
    },
    [employees],
  );

  const handleRemoveEmployee = useCallback((employeeId: string) => {
    setEmployees((prev) => prev.filter((emp) => emp.id !== employeeId));
  }, []);

  return (
    <div className="space-y-6">
      <PeopleSection
        title={title}
        placeholder={placeholder}
        employees={employees}
        onAddEmployee={handleAddEmployee}
        onRemoveEmployee={handleRemoveEmployee}
      />
    </div>
  );
};

export default PeopleTab;
