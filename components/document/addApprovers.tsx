import React, { useEffect, useState } from 'react';
import { Search, X, GripVertical, CheckCircle } from 'lucide-react';
import useFetch from '@/hooks/useFetch';
import { IUser } from '@/interfaces/user';
import { useAuthStore } from '@/globalProvider/authStore';

const AddApprovers = ({
  approvalType,
  approversData,
  existingApprovers = [],
  assignees = [],
}: {
  approvalType: string;
  approversData: any;
  existingApprovers?: any[];
  assignees?: any[];
}) => {
  // Determine if reordering is allowed based on approvalType
  const isReorderingAllowed = approvalType === 'Sequential';
  const accessToken = useAuthStore((state) => state.accessToken);

  const {
    data: users,
    isLoading,
    reFetch,
  } = useFetch<{ records: IUser[] }>(accessToken, `users`);

  // Enhanced approver type to include both IDs and status
  type EnhancedApprover = IUser & {
    record_id?: string; // Optional approval record ID
    status?: string;
  };

  // State for selected approvers, search query, and search results visibility
  const [approvers, setApprovers] = useState<EnhancedApprover[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [draggingIndex, setDraggingIndex] = useState(null);

  // Initialize approvers with existing data when component mounts
  useEffect(() => {
    if (existingApprovers && existingApprovers.length > 0) {
      // Separate approved and pending approvers
      const approvedApprovers: any = [];
      const pendingApprovers: any = [];

      existingApprovers.forEach((approver) => {
        const mappedApprover = {
          id: approver.user.id, // This is the user ID
          record_id: approver.id, // This is the approval record ID
          full_name: approver.user.full_name || approver.user.name,
          status: approver.status,
          // Add other properties if needed
        };

        if (approver.status === 'Approved') {
          approvedApprovers.push(mappedApprover);
        } else {
          pendingApprovers.push(mappedApprover);
        }
      });

      // Sort pending approvers by sequence number if it's sequential approval
      if (isReorderingAllowed) {
        pendingApprovers.sort((a: any, b: any) => {
          const aIndex = existingApprovers.findIndex(
            (item) => item.id === a.record_id,
          );
          const bIndex = existingApprovers.findIndex(
            (item) => item.id === b.record_id,
          );
          return (
            (existingApprovers[aIndex].sequence_number || aIndex) -
            (existingApprovers[bIndex].sequence_number || bIndex)
          );
        });
      }

      // Combine: approved approvers first, then pending approvers
      setApprovers([...approvedApprovers, ...pendingApprovers]);
    }
  }, [existingApprovers, isReorderingAllowed]);

  // Filter users based on search query
  const filteredUsers = users?.records.filter(
    (user) =>
      user?.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !approvers.some((approver) => approver.id === user.id) &&
      !assignees.some((assignee) => assignee.id === user.id),
  );

  // Handle adding a new approver
  const handleAddApprover = (user: any) => {
    setApprovers([
      ...approvers,
      {
        ...user,
        status: 'Pending',
        // No record_id for new approvers since they don't exist in the database yet
      },
    ]);
    setSearchQuery('');
    setShowResults(false);
  };

  // Handle removing an approver
  const handleRemoveApprover = (userId: string) => {
    // Only allow removing approvers that are not approved
    if (
      approvers.find((approver) => approver.id === userId)?.status ===
      'Approved'
    ) {
      return;
    }
    setApprovers(approvers.filter((approver) => approver.id !== userId));
  };

  // Handle drag start
  const handleDragStart = (index: any) => {
    // Don't allow dragging approved approvers
    if (!isReorderingAllowed || approvers[index]?.status === 'Approved') return;
    setDraggingIndex(index);
  };

  // Handle drag over
  const handleDragOver = (e: any, index: any) => {
    e.preventDefault();

    // Don't allow dropping on approved approvers or out of the pending section
    if (
      !isReorderingAllowed ||
      draggingIndex === null ||
      draggingIndex === index ||
      approvers[index]?.status === 'Approved'
    ) {
      return;
    }

    // Count approved approvers to maintain their position at the top
    const approvedCount = approvers.filter(
      (a) => a.status === 'Approved',
    ).length;

    // Don't allow moving items before approved approvers
    if (index < approvedCount) {
      return;
    }

    const newApprovers = [...approvers];
    const draggedItem = newApprovers[draggingIndex];

    // Remove the dragged item
    newApprovers.splice(draggingIndex, 1);
    // Insert it at the new position
    newApprovers.splice(index, 0, draggedItem);

    setApprovers(newApprovers);
    setDraggingIndex(index);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggingIndex(null);
  };

  useEffect(() => {
    // Send both the user ID and record ID (if available) to the parent component
    const formattedApprovers = approvers.map((approver, index) => ({
      user_id: approver.id,
      record_id: approver.record_id, // This will be undefined for new approvers
      sequence_number: index + 1,
      status: approver.status || 'Pending',
      full_name: approver.full_name,
    }));

    approversData(formattedApprovers);
  }, [approvers]);

  return (
    <div>
      <div className="relative mb-4">
        <h3 className="text-base font-medium text-approval-text-primary">
          Approvers
        </h3>
        <div className="py-2.5 px-3 bg-white flex items-center rounded-lg gap-1 border border-grey-100 max-w-[40rem] mt-4 focus-within:border-primary-100">
          <Search className="h-5 w-5" color="#B9B9B9" />
          <input
            type="text"
            placeholder="Add Approvers"
            className="flex-1 focus-visible:outline-none ml-1"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setShowResults(true);
            }}
            onFocus={() => setShowResults(true)}
          />
        </div>

        {/* Search results dropdown */}
        {showResults && searchQuery && (
          <div className="absolute w-[40rem] z-10 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {(filteredUsers ?? []).length > 0 ? (
              (filteredUsers ?? []).map((user) => (
                <div
                  key={user.id}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  onClick={() => handleAddApprover(user)}
                >
                  {user.full_name}
                </div>
              ))
            ) : (
              <div className="px-4 py-2 text-gray-500">No users found</div>
            )}
          </div>
        )}
      </div>

      {/* Selected approvers list */}
      <div className="space-y-2 mb-2">
        {approvers.map((approver, index) => (
          <div
            key={approver.id}
            className={`flex items-center p-3 rounded-lg ${
              approver.status === 'Approved' ? 'bg-gray-50' : 'bg-gray-50'
            } ${
              isReorderingAllowed && approver.status !== 'Approved'
                ? 'cursor-grab'
                : ''
            } ${draggingIndex === index ? 'opacity-50' : ''}`}
            draggable={isReorderingAllowed && approver.status !== 'Approved'}
            onDragStart={() => handleDragStart(index)}
            onDragOver={(e) => handleDragOver(e, index)}
            onDragEnd={handleDragEnd}
          >
            {isReorderingAllowed && approver.status !== 'Approved' && (
              <GripVertical className="h-5 w-5 text-gray-400 mr-2" />
            )}

            <div
              className={`${
                approver.status === 'Approved'
                  ? 'text-gray-300 cursor-not-allowed flex-1 ml-2'
                  : 'text-dark-300 flex-1 ml-2'
              }`}
            >
              {approver.full_name}
            </div>

            <button
              onClick={() => handleRemoveApprover(approver.id)}
              className={`ml-2 ${
                approver.status === 'Approved'
                  ? 'text-gray-300 cursor-not-allowed'
                  : 'text-gray-400 hover:text-gray-600'
              }`}
              disabled={approver.status === 'Approved'}
            >
              {approver.status === 'Approved' ? (
                'Approved'
              ) : (
                <X className="h-5 w-5" />
              )}
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AddApprovers;
