import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { AxiosError } from 'axios';

import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';
import ApprovalTypeSelector, { ApprovalType } from '../../approvalTypeSelector';
import AddApprovers from '../../addApprovers';
import InfoCircle from '@/assets/outline/infoCircle';
import { cn } from '@/utils/styleUtils';
import { usePut } from '@/hooks/usePut';
import { toast } from 'react-toastify';

interface ErrorResponse {
  error: string;
}

interface IProps {
  approvalId: string;
  setEditApproversModal: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
  existingApprovers: any;
  existingApprovalType: string;
  assignees?: any[];
}
const EditApproversModal = ({
  approvalId,
  setEditApproversModal,
  existingApprovers,
  existingApprovalType,
  refetch,
  assignees,
}: IProps) => {
  const [approvalType, setApprovalType] = useState<ApprovalType>(
    existingApprovalType as ApprovalType,
  );
  const [approversData, setApproversData] = useState([]);

  const { putData, error, response, isLoading } = usePut();

  const param = useParams();
  const { accessToken } = useAuthStore();

  const handleSubmit = async () => {
    const body = {
      flow: approvalType,
      approvers: approversData.map((user: any, index: number) => ({
        sequence_number: index + 1,
        user_id: user?.user_id,
        ...(user?.record_id && user?.record_id ? { id: user?.record_id } : {}),
      })),
    };

    await putData(accessToken as string, `approvals/${approvalId}`, body);
  };

  useEffect(() => {
    if (response) {
      toast.success('Approvers updated successfully');
      setEditApproversModal(false);
      refetch();
    }
    if (error) {
      toast.error(
        ((error as AxiosError).response?.data as ErrorResponse)?.error ||
          'An error occurred',
      );
    }
  }, [response, error]);
  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>Edit Approvers </DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <>
          <div className="flex-1 bg-yellow-100 rounded-md mb-5">
            <div className="flex gap-2 px-4 py-2 items-center text-base text-dark-300 leading-6 font-medium">
              <div
                className={cn(
                  'h-10 w-20 flex items-center justify-center bg-yellow-100 rounded-full',
                )}
              >
                <InfoCircle height="20" width="20" color="#F19413" />
              </div>
              <p className="text-base font-medium text-yellow-300">
                Only approvers who haven&apos;t taken any action on the document
                can be edited. To remove document approval, toggle it off
                through the document edit option.
              </p>
            </div>
          </div>
          <div className="mb-5">
            <div className="w-full">
              <ApprovalTypeSelector
                value={approvalType}
                onChange={setApprovalType}
              />
            </div>
          </div>

          <AddApprovers
            approvalType={approvalType}
            approversData={(data: any) => setApproversData(data)}
            existingApprovers={existingApprovers}
            assignees={assignees}
          />
        </>

        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={isLoading}
            disabled={approversData.length === 0}
            onClick={handleSubmit}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default EditApproversModal;
